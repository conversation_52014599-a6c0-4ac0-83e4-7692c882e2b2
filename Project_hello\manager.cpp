#include <iostream>
#include <fstream>
#include <vector>
#include "manager.h"
#include "tool.h"
#include "globalFile.h"

Manager::Manager(string name, string pwd)
{
	this->m_Name = name;
	this->m_Pwd = pwd;
}
Manager::~Manager()
{
}
void Manager::operMenu()
{
	int select;
	while (true)
	{
		system("cls");
		cout << "============================" << endl;
		cout << "1.add person" << endl;
		cout << "2.show person" << endl;
		cout << "3.show room" << endl;
		cout << "4.clean file" << endl;
		cout << "0.black" << endl;
		cout << "============================" << endl;
		select = getValidIntInput("please input:");
		switch (select)
		{
		case 1:
			addPerson();
			break;
		case 2:
			show<PERSON>erson();
			break;
		case 3:
			showRoom();
			break;
		case 4:
			cleanFile();
			break;
		case 0:
			system("cls");
			cout << "logout" << endl;
			return;
		}
	}
}
void Manager::addPerson()
{
	// 加载数据
	vector<FileUserInfo> users;
	string fName, _;
	fstream ifs(ADMIN_FILE, ios::in);
	if (!ifs.is_open())
	{
		cout << "open file failed" << endl;
		return;
	}
	while (ifs >> fName >> _)
	{
		users.emplace_back(FileUserInfo(0, fName, _));
	}
	ifs.close();

	string addName;
	while (true)
	{
		cout << "input 0 exit" << endl;
		addName = getValidStringInput("please input name: ");
		if (addName == "0")
		{
			return;
		}
		auto it = find_if(users.begin(), users.end(), [&](FileUserInfo &user)
						  { return user.name == addName; });
		if (it != users.end())
		{
			cout << "The name is already used, please reEnter!" << endl;
		}
		else
		{
			break;
		}
	}
	string addPwd = getValidStringInput("please input password: ");
	fstream ofs(ADMIN_FILE, ios::out | ios::app); // app追加模式
	if (!ofs.is_open())
	{
		cout << "open file failed" << endl;
		return;
	}
	try
	{
		ofs << endl << addName << " " << addPwd << endl;
		ofs.close();
		cout << "add success" << endl;
	}
	catch (const std::exception &e)
	{
		cout << "write Error: " << e.what() << endl;
		ofs.close();
		return;
	}
}

void Manager::showPerson()
{
	cout << "showPerson" << endl;
}
void Manager::showRoom()
{
}
void Manager::cleanFile()
{
}